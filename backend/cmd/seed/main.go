package main

import (
	"log"
	"ripple/pkg/auth"
	"ripple/pkg/config"
	"ripple/pkg/db"
	"ripple/pkg/models"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	log.Println("Starting database seeding...")

	// Load configuration
	cfg := config.LoadConfig()

	// Initialize database
	database, err := db.NewDatabase(cfg.DatabasePath)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.Close()

	// Run migrations first
	if err := database.RunMigrations(cfg.MigrationsPath); err != nil {
		log.Fatalf("Failed to run migrations: %v", err)
	}

	// Initialize repositories
	userRepo := models.NewUserRepository(database.DB)
	groupRepo := models.NewGroupRepository(database.DB)

	// Define seed users
	seedUsers := []struct {
		Email       string
		Password    string
		FirstName   string
		LastName    string
		DateOfBirth string
		Nickname    *string
		AboutMe     *string
	}{
		{
			Email:       "<EMAIL>",
			Password:    "password123",
			FirstName:   "Alice",
			LastName:    "<PERSON>",
			DateOfBirth: "1995-03-15",
			Nickname:    stringPtr("alice_j"),
			AboutMe:     stringPtr("Love photography and traveling. Always looking for new adventures!"),
		},
		{
			Email:       "<EMAIL>",
			Password:    "password123",
			FirstName:   "Bob",
			LastName:    "Smith",
			DateOfBirth: "1992-07-22",
			Nickname:    stringPtr("bob_smith"),
			AboutMe:     stringPtr("Software developer by day, gamer by night. Passionate about technology and innovation."),
		},
		{
			Email:       "<EMAIL>",
			Password:    "password123",
			FirstName:   "Charlie",
			LastName:    "Brown",
			DateOfBirth: "1998-11-08",
			Nickname:    stringPtr("charlie_b"),
			AboutMe:     stringPtr("Artist and musician. Creating beautiful things and sharing them with the world."),
		},
	}

	// Create users
	for _, seedUser := range seedUsers {
		// Check if user already exists
		existingUser, err := userRepo.GetUserByEmail(seedUser.Email)
		if err == nil && existingUser != nil {
			log.Printf("User %s already exists, skipping...", seedUser.Email)
			continue
		}

		// Hash password
		passwordHash, err := auth.HashPassword(seedUser.Password)
		if err != nil {
			log.Printf("Failed to hash password for %s: %v", seedUser.Email, err)
			continue
		}

		// Create user request
		createUserReq := &models.CreateUserRequest{
			Email:       seedUser.Email,
			Password:    seedUser.Password,
			FirstName:   seedUser.FirstName,
			LastName:    seedUser.LastName,
			DateOfBirth: seedUser.DateOfBirth,
			Nickname:    seedUser.Nickname,
			AboutMe:     seedUser.AboutMe,
		}

		// Create user
		user, err := userRepo.CreateUser(createUserReq, passwordHash)
		if err != nil {
			log.Printf("Failed to create user %s: %v", seedUser.Email, err)
			continue
		}

		log.Printf("Successfully created user: %s %s (%s)", user.FirstName, user.LastName, user.Email)
	}

	// Create sample groups
	log.Println("Creating sample groups...")
	err = createSampleGroups(userRepo, groupRepo)
	if err != nil {
		log.Printf("Failed to create sample groups: %v", err)
	}

	log.Println("Database seeding completed!")
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}

// Create sample groups
func createSampleGroups(userRepo *models.UserRepository, groupRepo *models.GroupRepository) error {
	// Get users for group creation
	alice, err := userRepo.GetUserByEmail("<EMAIL>")
	if err != nil {
		return err
	}

	bob, err := userRepo.GetUserByEmail("<EMAIL>")
	if err != nil {
		return err
	}

	// Define sample groups
	sampleGroups := []struct {
		CreatorID   int
		Title       string
		Description string
	}{
		{
			CreatorID:   alice.ID,
			Title:       "Photography Enthusiasts",
			Description: "A group for photography lovers to share tips, techniques, and showcase their work.",
		},
		{
			CreatorID:   bob.ID,
			Title:       "Tech Innovators",
			Description: "Discussing the latest in technology, programming, and innovation.",
		},
		{
			CreatorID:   alice.ID,
			Title:       "Travel Adventures",
			Description: "Share your travel experiences, tips, and discover new destinations together!",
		},
	}

	// Create groups
	for _, group := range sampleGroups {
		createGroupReq := &models.CreateGroupRequest{
			Title:       group.Title,
			Description: group.Description,
		}

		createdGroup, err := groupRepo.CreateGroup(group.CreatorID, createGroupReq)
		if err != nil {
			log.Printf("Failed to create group '%s': %v", group.Title, err)
			continue
		}

		log.Printf("Successfully created group: %s", createdGroup.Title)
	}

	return nil
}

// Create sample follow relationships
// func createSampleFollows(userRepo *models.UserRepository, followRepo *models.FollowRepository) error {
// 	// Get all users
// 	alice, err := userRepo.GetUserByEmail("<EMAIL>")
// 	if err != nil {
// 		return err
// 	}

// 	bob, err := userRepo.GetUserByEmail("<EMAIL>")
// 	if err != nil {
// 		return err
// 	}

// 	charlie, err := userRepo.GetUserByEmail("<EMAIL>")
// 	if err != nil {
// 		return err
// 	}

// 	// Create follow relationships
// 	followRelationships := []struct {
// 		FollowerID    int
// 		FollowingID   int
// 		FollowerName  string
// 		FollowingName string
// 	}{
// 		{alice.ID, bob.ID, "Alice", "Bob"},
// 		{alice.ID, charlie.ID, "Alice", "Charlie"},
// 		{bob.ID, alice.ID, "Bob", "Alice"},
// 		{bob.ID, charlie.ID, "Bob", "Charlie"},
// 		{charlie.ID, alice.ID, "Charlie", "Alice"},
// 	}

// 	// Create follows (these will be auto-accepted since users are public)
// 	for _, rel := range followRelationships {
// 		err := followRepo.CreateFollow(rel.FollowerID, rel.FollowingID)
// 		if err != nil {
// 			log.Printf("Failed to create follow relationship %s -> %s: %v", rel.FollowerName, rel.FollowingName, err)
// 			continue
// 		}

// 		log.Printf("Successfully created follow: %s is now following %s", rel.FollowerName, rel.FollowingName)
// 	}

// 	return nil
// }
