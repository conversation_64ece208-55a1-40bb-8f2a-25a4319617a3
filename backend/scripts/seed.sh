#!/bin/bash

# Database seeding script for Ripple Social Network

echo "🌱 Starting database seeding..."

# Change to the backend directory
cd "$(dirname "$0")/.."

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go first."
    exit 1
fi

# Run the seed script
echo "📦 Running seed script..."
go run cmd/seed/main.go

if [ $? -eq 0 ]; then
    echo "✅ Database seeding completed successfully!"
    echo ""
    echo "📋 Seeded users:"
    echo "   • <PERSON> (<EMAIL>) - password: password123"
    echo "   • <PERSON> (<EMAIL>) - password: password123"
    echo "   • <PERSON> (<EMAIL>) - password: password123"
    echo ""
    echo "🚀 You can now log in with any of these accounts!"
else
    echo "❌ Database seeding failed!"
    exit 1
fi
